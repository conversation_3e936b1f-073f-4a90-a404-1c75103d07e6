{<<"links">>,
 [{<<"GitHub">>,<<"https://github.com/ash-project/simple_sat">>},
  {<<"Discord">>,<<"https://discord.gg/HTHRaaVPUc">>},
  {<<"Website">>,<<"https://ash-hq.org">>},
  {<<"Forum">>,
   <<"https://elixirforum.com/c/elixir-framework-forums/ash-framework-forum">>}]}.
{<<"name">>,<<"simple_sat">>}.
{<<"version">>,<<"0.1.3">>}.
{<<"description">>,
 <<"A simple, dependency free boolean satisfiability solver.">>}.
{<<"elixir">>,<<"~> 1.13">>}.
{<<"app">>,<<"simple_sat">>}.
{<<"licenses">>,[<<"MIT">>]}.
{<<"files">>,
 [<<"lib">>,<<"lib/simple_sat.ex">>,<<".formatter.exs">>,<<"mix.exs">>,
  <<"README.md">>,<<"LICENSE">>,<<"CHANGELOG.md">>]}.
{<<"requirements">>,[]}.
{<<"build_tools">>,[<<"mix">>]}.
