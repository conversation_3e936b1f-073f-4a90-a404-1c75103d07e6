# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](Https://conventionalcommits.org) for commit guidelines.

<!-- changelog -->

## [v0.1.3](https://github.com/ash-project/simple_sat/compare/v0.1.2...v0.1.3) (2024-04-06)




### Bug Fixes:

* lower elixir version requirement

## [v0.1.2](https://github.com/ash-project/simple_sat/compare/v0.1.1...v0.1.2) (2024-04-05)




### Bug Fixes:

* fix logic bug for detecting truth when variable is false

## [v0.1.1](https://github.com/ash-project/simple_sat/compare/v0.1.0...v0.1.1) (2024-03-26)




### Bug Fixes:

* ensure variables come out sorted, as <PERSON> expects that and it won't hurt those that don't

## [v0.1.0](https://github.com/ash-project/simple_sat/compare/v0.1.0...v0.1.0) (2024-02-24)



