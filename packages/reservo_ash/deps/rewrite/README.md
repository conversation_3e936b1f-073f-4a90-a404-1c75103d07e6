# Rewrite
[![Hex.pm: version](https://img.shields.io/hexpm/v/rewrite.svg?style=flat-square)](https://hex.pm/packages/rewrite)
[![GitHub: CI status](https://img.shields.io/github/actions/workflow/status/hrzndhrn/rewrite/ci.yml?branch=main&style=flat-square)](https://github.com/hrzndhrn/rewrite/actions)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg?style=flat-square)](https://github.com/hrzndhrn//blob/main/LICENSE.md)

An API for rewriting sources in an Elixir project. Powered by
[`sourceror`](https://github.com/doorgan/sourceror).

Documentation can be found at [https://hexdocs.pm/rewrite](https://hexdocs.pm/rewrite).

## Installation

The package can be installed by adding `rewrite` to your list of
dependencies in `mix.exs`:

```elixir
def deps do
  [
    {:rewrite, "~> 1.0"}
  ]
end
```
