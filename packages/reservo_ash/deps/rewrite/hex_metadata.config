{<<"links">>,[{<<"GitHub">>,<<"https://github.com/hrzndhrn/rewrite">>}]}.
{<<"name">>,<<"rewrite">>}.
{<<"version">>,<<"1.1.2">>}.
{<<"description">>,
 <<"An API for rewriting sources in an Elixir project. Powered by sourceror.">>}.
{<<"elixir">>,<<"~> 1.13">>}.
{<<"app">>,<<"rewrite">>}.
{<<"licenses">>,[<<"MIT">>]}.
{<<"requirements">>,
 [[{<<"name">>,<<"glob_ex">>},
   {<<"app">>,<<"glob_ex">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 0.1">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"sourceror">>},
   {<<"app">>,<<"sourceror">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 1.0">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"text_diff">>},
   {<<"app">>,<<"text_diff">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 0.1">>},
   {<<"repository">>,<<"hexpm">>}]]}.
{<<"files">>,
 [<<"lib">>,<<"lib/rewrite">>,<<"lib/rewrite/filetype.ex">>,
  <<"lib/rewrite/hook.ex">>,<<"lib/rewrite/source">>,
  <<"lib/rewrite/source/ex.ex">>,<<"lib/rewrite/error.ex">>,
  <<"lib/rewrite/update_error.ex">>,<<"lib/rewrite/source_key_error.ex">>,
  <<"lib/rewrite/dot_formatter.ex">>,<<"lib/rewrite/dot_formatter_error.ex">>,
  <<"lib/rewrite/source.ex">>,<<"lib/rewrite/source_error.ex">>,
  <<"lib/rewrite/application.ex">>,<<"lib/rewrite.ex">>,<<"lib/hook">>,
  <<"lib/hook/dot_formatter_updater.ex">>,<<".formatter.exs">>,<<"mix.exs">>,
  <<"README.md">>,<<"LICENSE.md">>,<<"CHANGELOG.md">>]}.
{<<"build_tools">>,[<<"mix">>]}.
