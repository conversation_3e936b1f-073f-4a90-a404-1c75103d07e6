{<<"links">>,[{<<"GitHub">>,<<"https://github.com/doorgan/sourceror">>}]}.
{<<"name">>,<<"sourceror">>}.
{<<"version">>,<<"1.10.0">>}.
{<<"description">>,<<"Utilities to work with Elixir source code.">>}.
{<<"elixir">>,<<"~> 1.12">>}.
{<<"app">>,<<"sourceror">>}.
{<<"licenses">>,[<<"Apache-2.0">>]}.
{<<"requirements">>,[]}.
{<<"files">>,
 [<<"lib">>,<<"lib/sourceror.ex">>,<<"lib/sourceror">>,
  <<"lib/sourceror/identifier.ex">>,<<"lib/sourceror/utils">>,
  <<"lib/sourceror/utils/typedstruct.ex">>,<<"lib/sourceror/zipper">>,
  <<"lib/sourceror/zipper/inspect.ex">>,<<"lib/sourceror/range.ex">>,
  <<"lib/sourceror/zipper.ex">>,<<"lib/sourceror/code.ex">>,
  <<"lib/sourceror/lines_corrector.ex">>,<<"lib/sourceror/patch.ex">>,
  <<"lib/sourceror/comments.ex">>,<<"lib/sourceror/fast_zipper.ex">>,
  <<"lib/sourceror/traversal_state.ex">>,<<".formatter.exs">>,<<"mix.exs">>,
  <<"README.md">>,<<"LICENSE">>,<<"CHANGELOG.md">>]}.
{<<"build_tools">>,[<<"mix">>]}.
