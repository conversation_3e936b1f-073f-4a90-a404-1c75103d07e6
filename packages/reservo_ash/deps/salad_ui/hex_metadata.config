{<<"app">>,<<"salad_ui">>}.
{<<"build_tools">>,[<<"mix">>]}.
{<<"description">>,<<"Phoenix UI components library inspired by shadcn ui">>}.
{<<"elixir">>,<<"~> 1.14">>}.
{<<"files">>,
 [<<"lib">>,<<"lib/salad_ui.ex">>,<<"lib/mix">>,<<"lib/mix/tasks">>,
  <<"lib/mix/tasks/salad.install.ex">>,<<"lib/mix/tasks/salad.setup.ex">>,
  <<"lib/mix/tasks/helpers">>,<<"lib/mix/tasks/helpers/tailwind_patcher.ex">>,
  <<"lib/mix/tasks/helpers/js_patcher.ex">>,<<"lib/salad_ui">>,
  <<"lib/salad_ui/collapsible.ex">>,<<"lib/salad_ui/skeleton.ex">>,
  <<"lib/salad_ui/alert_dialog.ex">>,<<"lib/salad_ui/chart.ex">>,
  <<"lib/salad_ui/avatar.ex">>,<<"lib/salad_ui/switch.ex">>,
  <<"lib/salad_ui/badge.ex">>,<<"lib/salad_ui/tooltip.ex">>,
  <<"lib/salad_ui/liveview.ex">>,<<"lib/salad_ui/sidebar.ex">>,
  <<"lib/salad_ui/icon.ex">>,<<"lib/salad_ui/separator.ex">>,
  <<"lib/salad_ui/toggle.ex">>,<<"lib/salad_ui/breadcrumb.ex">>,
  <<"lib/salad_ui/textarea.ex">>,<<"lib/salad_ui/dropdown_menu.ex">>,
  <<"lib/salad_ui/command.ex">>,<<"lib/salad_ui/tabs.ex">>,
  <<"lib/salad_ui/dialog.ex">>,<<"lib/salad_ui/radio_group.ex">>,
  <<"lib/salad_ui/select.ex">>,<<"lib/salad_ui/progress.ex">>,
  <<"lib/salad_ui/pagination.ex">>,<<"lib/salad_ui/hover_card.ex">>,
  <<"lib/salad_ui/popover.ex">>,<<"lib/salad_ui/table.ex">>,
  <<"lib/salad_ui/helpers.ex">>,<<"lib/salad_ui/toggle_group.ex">>,
  <<"lib/salad_ui/alert.ex">>,<<"lib/salad_ui/checkbox.ex">>,
  <<"lib/salad_ui/menu.ex">>,<<"lib/salad_ui/slider.ex">>,
  <<"lib/salad_ui/card.ex">>,<<"lib/salad_ui/scroll_area.ex">>,
  <<"lib/salad_ui/button.ex">>,<<"lib/salad_ui/form.ex">>,
  <<"lib/salad_ui/sheet.ex">>,<<"lib/salad_ui/input.ex">>,
  <<"lib/salad_ui/label.ex">>,<<"lib/salad_ui/accordion.ex">>,
  <<"assets/salad_ui">>,<<"assets/salad_ui/core">>,
  <<"assets/salad_ui/core/factory.js">>,
  <<"assets/salad_ui/core/state-machine.js">>,
  <<"assets/salad_ui/core/hook.js">>,<<"assets/salad_ui/core/positioner.js">>,
  <<"assets/salad_ui/core/positioned-element.js">>,
  <<"assets/salad_ui/core/portal.js">>,
  <<"assets/salad_ui/core/component.js">>,
  <<"assets/salad_ui/core/focus-trap.js">>,
  <<"assets/salad_ui/core/utils.js">>,
  <<"assets/salad_ui/core/scroll-manager.js">>,
  <<"assets/salad_ui/core/collection.js">>,
  <<"assets/salad_ui/core/click-outside.js">>,<<"assets/salad_ui/index.js">>,
  <<"assets/salad_ui/components">>,
  <<"assets/salad_ui/components/collapsible.js">>,
  <<"assets/salad_ui/components/switch.js">>,
  <<"assets/salad_ui/components/tooltip.js">>,
  <<"assets/salad_ui/components/chart.js">>,
  <<"assets/salad_ui/components/dropdown_menu.js">>,
  <<"assets/salad_ui/components/dialog.js">>,
  <<"assets/salad_ui/components/tabs.js">>,
  <<"assets/salad_ui/components/radio_group.js">>,
  <<"assets/salad_ui/components/command.js">>,
  <<"assets/salad_ui/components/popover.js">>,
  <<"assets/salad_ui/components/select.js">>,
  <<"assets/salad_ui/components/menu.js">>,
  <<"assets/salad_ui/components/slider.js">>,
  <<"assets/salad_ui/components/hover-card.js">>,
  <<"assets/salad_ui/components/accordion.js">>,<<"priv">>,<<"priv/static">>,
  <<"priv/static/assets">>,<<"priv/static/assets/tailwind.colors.json">>,
  <<"priv/static/assets/salad_ui.css">>,<<"priv/static/assets/colors">>,
  <<"priv/static/assets/colors/yellow.css">>,
  <<"priv/static/assets/colors/blue.css">>,
  <<"priv/static/assets/colors/neutral.css">>,
  <<"priv/static/assets/colors/gray.css">>,
  <<"priv/static/assets/colors/rose.css">>,
  <<"priv/static/assets/colors/zinc.css">>,
  <<"priv/static/assets/colors/red.css">>,
  <<"priv/static/assets/colors/orange.css">>,
  <<"priv/static/assets/colors/slate.css">>,
  <<"priv/static/assets/colors/stone.css">>,
  <<"priv/static/assets/colors/violet.css">>,
  <<"priv/static/assets/colors/green.css">>,<<".formatter.exs">>,
  <<"mix.exs">>,<<"README.md">>,<<"CHANGELOG.md">>,<<"LICENSE">>,
  <<"package.json">>]}.
{<<"licenses">>,[<<"MIT">>]}.
{<<"links">>,[{<<"GitHub">>,<<"https://github.com/bluzky/salad_ui">>}]}.
{<<"name">>,<<"salad_ui">>}.
{<<"requirements">>,
 [[{<<"app">>,<<"tw_merge">>},
   {<<"name">>,<<"tw_merge">>},
   {<<"optional">>,false},
   {<<"repository">>,<<"hexpm">>},
   {<<"requirement">>,<<"~> 0.1">>}],
  [{<<"app">>,<<"phoenix_live_view">>},
   {<<"name">>,<<"phoenix_live_view">>},
   {<<"optional">>,false},
   {<<"repository">>,<<"hexpm">>},
   {<<"requirement">>,<<"~> 1.0">>}],
  [{<<"app">>,<<"igniter">>},
   {<<"name">>,<<"igniter">>},
   {<<"optional">>,false},
   {<<"repository">>,<<"hexpm">>},
   {<<"requirement">>,<<"~> 0.5">>}],
  [{<<"app">>,<<"sourceror">>},
   {<<"name">>,<<"sourceror">>},
   {<<"optional">>,false},
   {<<"repository">>,<<"hexpm">>},
   {<<"requirement">>,<<"~> 1.9">>}]]}.
{<<"version">>,<<"1.0.0-beta.3">>}.
