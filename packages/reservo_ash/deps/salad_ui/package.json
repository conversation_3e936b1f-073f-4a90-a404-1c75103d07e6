{"name": "salad_ui", "version": "1.0.0", "description": "Phoenix LiveView components library and framework", "main": "index.js", "files": ["README.md", "LICENSE.md", "package.json", "priv/static/*", "assets/*", "assets/salad_ui/*", "assets/salad_ui/core/*", "assets/salad_ui/components/*"], "exports": {".": "./assets/salad_ui/index.js", "./components/*": "./assets/salad_ui/components/*.js", "./core/*": "./assets/salad_ui/core/*.js"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/bluzky/salad_ui.git"}, "keywords": ["liveview", "phoenix", "framework", "salad_ui"], "author": "<PERSON><PERSON><PERSON> (<PERSON>)", "license": "MIT", "bugs": {"url": "https://github.com/bluzky/salad_ui/issues"}, "homepage": "https://github.com/bluzky/salad_ui#readme"}