{"accent": {"DEFAULT": "hsl(var(--accent))", "foreground": "hsl(var(--accent-foreground))"}, "background": "hsl(var(--background))", "border": "hsl(var(--border))", "card": {"DEFAULT": "hsl(var(--card))", "foreground": "hsl(var(--card-foreground))"}, "destructive": {"DEFAULT": "hsl(var(--destructive))", "foreground": "hsl(var(--destructive-foreground))"}, "foreground": "hsl(var(--foreground))", "input": "hsl(var(--input))", "muted": {"DEFAULT": "hsl(var(--muted))", "foreground": "hsl(var(--muted-foreground))"}, "popover": {"DEFAULT": "hsl(var(--popover))", "foreground": "hsl(var(--popover-foreground))"}, "primary": {"DEFAULT": "hsl(var(--primary))", "foreground": "hsl(var(--primary-foreground))"}, "ring": "hsl(var(--ring))", "secondary": {"DEFAULT": "hsl(var(--secondary))", "foreground": "hsl(var(--secondary-foreground))"}, "sidebar": {"DEFAULT": "hsl(var(--sidebar-background))", "foreground": "hsl(var(--sidebar-foreground))", "primary": "hsl(var(--sidebar-primary))", "primary-foreground": "hsl(var(--sidebar-primary-foreground))", "accent": "hsl(var(--sidebar-accent))", "accent-foreground": "hsl(var(--sidebar-accent-foreground))", "border": "hsl(var(--sidebar-border))", "ring": "hsl(var(--sidebar-ring))"}}